import pandas as pd

# 读取原始文件
print("正在读取原始文件...")
df = pd.read_csv('原文作者综合统计表.csv')

print(f"原始数据形状: {df.shape}")
print(f"原始列名: {df.columns.tolist()}")

# 删除第一行（索引为0的行）
df_modified = df.drop(index=0)

# 删除最后一列
df_modified = df_modified.drop(columns=df_modified.columns[-1])

print(f"\n修改后数据形状: {df_modified.shape}")
print(f"修改后列名: {df_modified.columns.tolist()}")

print(f"\n修改后的前5行:")
print(df_modified.head())

print(f"\n修改后的最后5行:")
print(df_modified.tail())

# 保存修改后的文件
output_filename = '原文作者综合统计表_修改版.csv'
df_modified.to_csv(output_filename, index=False, encoding='utf-8-sig')

print(f"\n修改后的文件已保存为: {output_filename}")

# 验证保存的文件
print(f"\n验证保存的文件:")
df_verify = pd.read_csv(output_filename)
print(f"验证文件形状: {df_verify.shape}")
print(f"验证文件列名: {df_verify.columns.tolist()}")
