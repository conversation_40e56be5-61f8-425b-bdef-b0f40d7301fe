import pandas as pd
import matplotlib.pyplot as plt

# 1. 读取 Excel 文件（假设你导出为 .xlsx 或 .csv）
df = pd.read_excel('1.xlsx')  # 或 pd.read_csv('your_file.csv')

# 2. 解析“日期”列为 datetime 类型（列名为“日期”）
df['日期'] = pd.to_datetime(df['日期'], errors='coerce')

# 3. 按日期统计每一天的声量（数量）
df['日期_日'] = df['日期'].dt.date  # 只保留年月日
daily_counts = df.groupby('日期_日').size().reset_index(name='声量')

# 4. 画折线图
plt.figure(figsize=(12, 6))
plt.plot(daily_counts['日期_日'], daily_counts['声量'], marker='o')
plt.xticks(rotation=45)
plt.title('舆论生命周期：每日声量变化')
plt.xlabel('日期')
plt.ylabel('声量（条）')
plt.grid(True)
plt.tight_layout()
plt.show()

# 基于声量变化的四阶段划分：在原图基础上添加四个区域
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 重新绘制带四阶段划分的图
plt.figure(figsize=(14, 8))

# 基于声量变化划分四个阶段
volumes = daily_counts['声量'].values
dates = daily_counts['日期_日'].tolist()

# 找到声量最大值的位置
peak_idx = np.argmax(volumes)
peak_value = volumes[peak_idx]

# 潜伏期：从开始到声量首次超过峰值的30%
latent_end = 0
for i in range(len(volumes)):
    if volumes[i] > peak_value * 0.3:
        latent_end = i
        break

# 发展期：从潜伏期结束到峰值
growth_end = peak_idx

# 成熟期：从峰值到声量下降到峰值70%的位置
mature_end = peak_idx
for i in range(peak_idx + 1, len(volumes)):
    if volumes[i] < peak_value * 0.7:
        mature_end = i
        break
else:
    mature_end = min(peak_idx + len(volumes)//8, len(volumes)-1)

# 获取对应的日期
date1 = dates[0]
date2 = dates[latent_end]
date3 = dates[growth_end]
date4 = dates[mature_end]
date5 = dates[-1]

# 绘制四个阶段的背景色
plt.axvspan(date1, date2, alpha=0.2, color='#1976D2', label='潜伏期')
plt.axvspan(date2, date3, alpha=0.2, color='#F57C00', label='发展期')
plt.axvspan(date3, date4, alpha=0.2, color='#388E3C', label='成熟期')
plt.axvspan(date4, date5, alpha=0.2, color='#D32F2F', label='消散期')

# 绘制原始折线图
plt.plot(daily_counts['日期_日'], daily_counts['声量'], 'o-', color='black', linewidth=2, markersize=4)

# 在每个阶段中间添加文字标注
mid_dates = [
    dates[latent_end//2] if latent_end > 0 else dates[0],
    dates[(latent_end + growth_end)//2],
    dates[(growth_end + mature_end)//2],
    dates[(mature_end + len(dates)-1)//2]
]

phase_names = ['潜伏期', '发展期', '成熟期', '消散期']
colors = ['#1976D2', '#F57C00', '#388E3C', '#D32F2F']

# 获取y轴的最大值用于定位文字
max_volume = daily_counts['声量'].max()

for i, (mid_date, phase_name, color) in enumerate(zip(mid_dates, phase_names, colors)):
    plt.text(mid_date, max_volume * 0.9, phase_name, 
             fontsize=14, fontweight='bold', color=color,
             ha='center', va='center',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8, edgecolor=color))

plt.xticks(rotation=45)
plt.title('舆论生命周期四阶段分析\n潜伏期 → 发展期 → 成熟期 → 消散期', fontsize=16, fontweight='bold')
plt.xlabel('日期', fontsize=12)
plt.ylabel('声量（条）', fontsize=12)
plt.grid(True, alpha=0.3)
plt.legend(loc='upper right')
plt.tight_layout()
plt.show()

# 保存图片
plt.savefig('生命周期四阶段图.png', dpi=300, bbox_inches='tight')
print("图表已保存为 '生命周期四阶段图.png'")

# 打印阶段划分信息
print(f"\n阶段划分结果：")
print(f"潜伏期: {date1} 至 {date2} (索引 0-{latent_end})")
print(f"发展期: {date2} 至 {date3} (索引 {latent_end}-{growth_end})")
print(f"成熟期: {date3} 至 {date4} (索引 {growth_end}-{mature_end})")
print(f"消散期: {date4} 至 {date5} (索引 {mature_end}-{len(dates)-1})")
print(f"声量峰值: {peak_value} (出现在索引 {peak_idx}, 日期 {dates[peak_idx]})")