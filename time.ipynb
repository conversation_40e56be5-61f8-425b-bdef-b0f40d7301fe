import pandas as pd
import matplotlib.pyplot as plt

# 1. 读取 Excel 文件（假设你导出为 .xlsx 或 .csv）
df = pd.read_excel('1.xlsx')  # 或 pd.read_csv('your_file.csv')

# 2. 解析“日期”列为 datetime 类型（列名为“日期”）
df['日期'] = pd.to_datetime(df['日期'], errors='coerce')

# 3. 按日期统计每一天的声量（数量）
df['日期_日'] = df['日期'].dt.date  # 只保留年月日
daily_counts = df.groupby('日期_日').size().reset_index(name='声量')

# 4. 画折线图
plt.figure(figsize=(12, 6))
plt.plot(daily_counts['日期_日'], daily_counts['声量'], marker='o')
plt.xticks(rotation=45)
plt.title('舆论生命周期：每日声量变化')
plt.xlabel('日期')
plt.ylabel('声量（条）')
plt.grid(True)
plt.tight_layout()
plt.show()

# 简单的四阶段划分：在原图基础上添加四个区域
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 重新绘制带四阶段划分的图
plt.figure(figsize=(14, 8))

# 简单按时间等分为四个阶段
total_days = len(daily_counts)
quarter = total_days // 4

# 定义四个阶段的时间分界点
phase1_end = quarter
phase2_end = quarter * 2
phase3_end = quarter * 3
phase4_end = total_days

# 获取对应的日期
dates = daily_counts['日期_日'].tolist()
date1 = dates[0] if phase1_end > 0 else dates[0]
date2 = dates[phase1_end-1] if phase1_end < len(dates) else dates[-1]
date3 = dates[phase2_end-1] if phase2_end < len(dates) else dates[-1]
date4 = dates[phase3_end-1] if phase3_end < len(dates) else dates[-1]
date5 = dates[-1]

# 绘制四个阶段的背景色
plt.axvspan(date1, date2, alpha=0.2, color='#1976D2', label='潜伏期')
plt.axvspan(date2, date3, alpha=0.2, color='#F57C00', label='发展期')
plt.axvspan(date3, date4, alpha=0.2, color='#388E3C', label='成熟期')
plt.axvspan(date4, date5, alpha=0.2, color='#D32F2F', label='消散期')

# 绘制原始折线图
plt.plot(daily_counts['日期_日'], daily_counts['声量'], 'o-', color='black', linewidth=2, markersize=4)

# 在每个阶段中间添加文字标注
mid_dates = [
    dates[quarter//2] if quarter//2 < len(dates) else dates[0],
    dates[quarter + quarter//2] if quarter + quarter//2 < len(dates) else dates[quarter],
    dates[quarter*2 + quarter//2] if quarter*2 + quarter//2 < len(dates) else dates[quarter*2],
    dates[quarter*3 + quarter//2] if quarter*3 + quarter//2 < len(dates) else dates[quarter*3]
]

phase_names = ['潜伏期', '发展期', '成熟期', '消散期']
colors = ['#1976D2', '#F57C00', '#388E3C', '#D32F2F']

# 获取y轴的最大值用于定位文字
max_volume = daily_counts['声量'].max()

for i, (mid_date, phase_name, color) in enumerate(zip(mid_dates, phase_names, colors)):
    plt.text(mid_date, max_volume * 0.9, phase_name, 
             fontsize=14, fontweight='bold', color=color,
             ha='center', va='center',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8, edgecolor=color))

plt.xticks(rotation=45)
plt.title('舆论生命周期四阶段分析\n潜伏期 → 发展期 → 成熟期 → 消散期', fontsize=16, fontweight='bold')
plt.xlabel('日期', fontsize=12)
plt.ylabel('声量（条）', fontsize=12)
plt.grid(True, alpha=0.3)
plt.legend(loc='upper right')
plt.tight_layout()
plt.show()

# 保存图片
plt.savefig('生命周期四阶段图.png', dpi=300, bbox_inches='tight')
print("图表已保存为 '生命周期四阶段图.png'")