import pandas as pd
import matplotlib.pyplot as plt

# 1. 读取 Excel 文件（假设你导出为 .xlsx 或 .csv）
df = pd.read_excel('1.xlsx')  # 或 pd.read_csv('your_file.csv')

# 2. 解析“日期”列为 datetime 类型（列名为“日期”）
df['日期'] = pd.to_datetime(df['日期'], errors='coerce')

# 3. 按日期统计每一天的声量（数量）
df['日期_日'] = df['日期'].dt.date  # 只保留年月日
daily_counts = df.groupby('日期_日').size().reset_index(name='声量')

# 4. 画折线图
plt.figure(figsize=(12, 6))
plt.plot(daily_counts['日期_日'], daily_counts['声量'], marker='o')
plt.xticks(rotation=45)
plt.title('舆论生命周期：每日声量变化')
plt.xlabel('日期')
plt.ylabel('声量（条）')
plt.grid(True)
plt.tight_layout()
plt.show()

# 添加生命周期四阶段划分功能
import numpy as np
from scipy.signal import find_peaks

def identify_lifecycle_phases(daily_counts):
    """
    识别生命周期的四个阶段：潜伏期、发展期、成熟期、消散期
    """
    # 获取声量数据
    volumes = daily_counts['声量'].values
    dates = daily_counts['日期_日'].values
    
    # 计算移动平均以平滑数据
    window_size = min(7, len(volumes) // 4)  # 7天移动平均或数据长度的1/4
    if window_size < 3:
        window_size = 3
    
    smoothed_volumes = np.convolve(volumes, np.ones(window_size)/window_size, mode='same')
    
    # 找到峰值点
    peaks, _ = find_peaks(smoothed_volumes, height=np.mean(smoothed_volumes))
    
    # 如果没有找到明显峰值，使用最大值点
    if len(peaks) == 0:
        peak_idx = np.argmax(smoothed_volumes)
    else:
        # 选择最高的峰值
        peak_idx = peaks[np.argmax(smoothed_volumes[peaks])]
    
    # 定义四个阶段的分界点
    total_length = len(volumes)
    
    # 潜伏期：从开始到声量开始显著上升
    # 寻找第一个显著上升点（声量超过初期平均值的1.5倍）
    initial_avg = np.mean(volumes[:min(5, total_length//4)])
    growth_start = 0
    for i in range(1, total_length):
        if volumes[i] > initial_avg * 1.5:
            growth_start = i
            break
    
    # 发展期：从显著上升到峰值
    growth_end = peak_idx
    
    # 成熟期：峰值附近的稳定期
    # 寻找声量开始持续下降的点
    mature_end = peak_idx
    peak_value = smoothed_volumes[peak_idx]
    for i in range(peak_idx + 1, total_length):
        if smoothed_volumes[i] < peak_value * 0.7:  # 下降到峰值的70%
            mature_end = i
            break
    
    # 消散期：从成熟期结束到数据结束
    decline_end = total_length - 1
    
    phases = {
        '潜伏期': (0, growth_start),
        '发展期': (growth_start, growth_end),
        '成熟期': (growth_end, mature_end),
        '消散期': (mature_end, decline_end)
    }
    
    return phases, smoothed_volumes

# 识别生命周期阶段
phases, smoothed_data = identify_lifecycle_phases(daily_counts)

print("生命周期四阶段划分结果：")
for phase_name, (start_idx, end_idx) in phases.items():
    start_date = daily_counts.iloc[start_idx]['日期_日']
    end_date = daily_counts.iloc[end_idx]['日期_日']
    duration = end_idx - start_idx + 1
    avg_volume = daily_counts.iloc[start_idx:end_idx+1]['声量'].mean()
    print(f"{phase_name}: {start_date} 至 {end_date} ({duration}天, 平均声量: {avg_volume:.1f})")

# 绘制带有四阶段划分的生命周期图
plt.figure(figsize=(15, 8))

# 定义四个阶段的颜色
phase_colors = {
    '潜伏期': '#E3F2FD',  # 浅蓝色
    '发展期': '#FFF3E0',  # 浅橙色
    '成熟期': '#E8F5E8',  # 浅绿色
    '消散期': '#FFEBEE'   # 浅红色
}

phase_line_colors = {
    '潜伏期': '#1976D2',  # 蓝色
    '发展期': '#F57C00',  # 橙色
    '成熟期': '#388E3C',  # 绿色
    '消散期': '#D32F2F'   # 红色
}

# 绘制背景区域
for phase_name, (start_idx, end_idx) in phases.items():
    start_date = daily_counts.iloc[start_idx]['日期_日']
    end_date = daily_counts.iloc[end_idx]['日期_日']
    plt.axvspan(start_date, end_date, alpha=0.3, color=phase_line_colors[phase_name], label=phase_name)

# 绘制原始数据线
plt.plot(daily_counts['日期_日'], daily_counts['声量'], 'o-', color='#2C3E50', linewidth=2, markersize=4, label='实际声量')

# 绘制平滑后的趋势线
plt.plot(daily_counts['日期_日'], smoothed_data, '--', color='red', linewidth=3, alpha=0.7, label='趋势线')

# 标注各阶段
for phase_name, (start_idx, end_idx) in phases.items():
    mid_idx = (start_idx + end_idx) // 2
    mid_date = daily_counts.iloc[mid_idx]['日期_日']
    mid_volume = daily_counts.iloc[mid_idx]['声量']
    
    # 在图上标注阶段名称
    plt.annotate(phase_name, 
                xy=(mid_date, mid_volume), 
                xytext=(10, 20), 
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=phase_colors[phase_name], alpha=0.8),
                fontsize=12, fontweight='bold',
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

plt.xticks(rotation=45)
plt.title('舆论生命周期四阶段分析图\n(潜伏期 → 发展期 → 成熟期 → 消散期)', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('日期', fontsize=12, fontweight='bold')
plt.ylabel('声量（条）', fontsize=12, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.legend(loc='upper right', fontsize=10)
plt.tight_layout()
plt.show()

# 保存图片
plt.savefig('舆论生命周期四阶段分析.png', dpi=300, bbox_inches='tight')
print("\n图表已保存为 '舆论生命周期四阶段分析.png'")

# 生成各阶段统计分析
import matplotlib.pyplot as plt

# 计算各阶段统计数据
phase_stats = {}
for phase_name, (start_idx, end_idx) in phases.items():
    phase_data = daily_counts.iloc[start_idx:end_idx+1]['声量']
    phase_stats[phase_name] = {
        '持续天数': len(phase_data),
        '平均声量': phase_data.mean(),
        '最大声量': phase_data.max(),
        '最小声量': phase_data.min(),
        '总声量': phase_data.sum(),
        '声量标准差': phase_data.std()
    }

# 创建统计图表
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

phases_list = list(phase_stats.keys())
colors = ['#1976D2', '#F57C00', '#388E3C', '#D32F2F']

# 1. 各阶段持续天数
durations = [phase_stats[phase]['持续天数'] for phase in phases_list]
bars1 = ax1.bar(phases_list, durations, color=colors, alpha=0.7)
ax1.set_title('各阶段持续天数', fontsize=12, fontweight='bold')
ax1.set_ylabel('天数')
for bar, value in zip(bars1, durations):
    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
            f'{value}天', ha='center', va='bottom', fontweight='bold')

# 2. 各阶段平均声量
avg_volumes = [phase_stats[phase]['平均声量'] for phase in phases_list]
bars2 = ax2.bar(phases_list, avg_volumes, color=colors, alpha=0.7)
ax2.set_title('各阶段平均声量', fontsize=12, fontweight='bold')
ax2.set_ylabel('平均声量')
for bar, value in zip(bars2, avg_volumes):
    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(avg_volumes)*0.01, 
            f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

# 3. 各阶段总声量
total_volumes = [phase_stats[phase]['总声量'] for phase in phases_list]
bars3 = ax3.bar(phases_list, total_volumes, color=colors, alpha=0.7)
ax3.set_title('各阶段总声量', fontsize=12, fontweight='bold')
ax3.set_ylabel('总声量')
for bar, value in zip(bars3, total_volumes):
    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(total_volumes)*0.01, 
            f'{value}', ha='center', va='bottom', fontweight='bold')

# 4. 各阶段声量波动性（标准差）
volatilities = [phase_stats[phase]['声量标准差'] for phase in phases_list]
bars4 = ax4.bar(phases_list, volatilities, color=colors, alpha=0.7)
ax4.set_title('各阶段声量波动性', fontsize=12, fontweight='bold')
ax4.set_ylabel('标准差')
for bar, value in zip(bars4, volatilities):
    ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(volatilities)*0.01, 
            f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.show()

# 保存统计图
plt.savefig('生命周期各阶段统计分析.png', dpi=300, bbox_inches='tight')
print("统计分析图已保存为 '生命周期各阶段统计分析.png'")

# 输出详细的阶段分析报告
print("="*60)
print("           舆论生命周期四阶段分析报告")
print("="*60)

total_days = len(daily_counts)
total_volume = daily_counts['声量'].sum()

for i, (phase_name, (start_idx, end_idx)) in enumerate(phases.items(), 1):
    stats = phase_stats[phase_name]
    start_date = daily_counts.iloc[start_idx]['日期_日']
    end_date = daily_counts.iloc[end_idx]['日期_日']
    
    print(f"\n{i}. {phase_name}")
    print(f"   时间范围: {start_date} 至 {end_date}")
    print(f"   持续天数: {stats['持续天数']}天 ({stats['持续天数']/total_days*100:.1f}% 的总时长)")
    print(f"   声量统计:")
    print(f"     - 总声量: {stats['总声量']} ({stats['总声量']/total_volume*100:.1f}% 的总声量)")
    print(f"     - 平均声量: {stats['平均声量']:.1f}")
    print(f"     - 最大声量: {stats['最大声量']}")
    print(f"     - 最小声量: {stats['最小声量']}")
    print(f"     - 波动性(标准差): {stats['声量标准差']:.1f}")
    
    # 阶段特征分析
    if phase_name == '潜伏期':
        print(f"   阶段特征: 舆论处于萌芽状态，声量较低且相对稳定")
    elif phase_name == '发展期':
        print(f"   阶段特征: 舆论快速发酵，声量呈上升趋势")
    elif phase_name == '成熟期':
        print(f"   阶段特征: 舆论达到高峰，声量处于最高水平")
    elif phase_name == '消散期':
        print(f"   阶段特征: 舆论热度下降，声量逐渐减少")

print(f"\n\n总结:")
print(f"- 整个生命周期共 {total_days} 天")
print(f"- 总声量 {total_volume} 条")
print(f"- 日均声量 {total_volume/total_days:.1f} 条")
print(f"- 峰值出现在 {phases['成熟期'][0]} 到 {phases['成熟期'][1]} 索引区间")

# 找出声量最高的日期
max_volume_idx = daily_counts['声量'].idxmax()
max_volume_date = daily_counts.loc[max_volume_idx, '日期_日']
max_volume_value = daily_counts.loc[max_volume_idx, '声量']
print(f"- 声量峰值: {max_volume_date} ({max_volume_value} 条)")

print("\n" + "="*60)