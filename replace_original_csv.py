import pandas as pd
import shutil

# 备份原文件
print("正在备份原文件...")
shutil.copy('原文作者综合统计表.csv', '原文作者综合统计表_备份.csv')
print("原文件已备份为: 原文作者综合统计表_备份.csv")

# 读取原始文件
df = pd.read_csv('原文作者综合统计表.csv')

print(f"原始数据形状: {df.shape}")

# 删除第一行和最后一列
df_modified = df.drop(index=0).drop(columns=df.columns[-1])

print(f"修改后数据形状: {df_modified.shape}")

# 覆盖原文件
df_modified.to_csv('原文作者综合统计表.csv', index=False, encoding='utf-8-sig')

print("原文件已更新完成！")
print("如需恢复，请使用备份文件: 原文作者综合统计表_备份.csv")
