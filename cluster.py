import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, Normalizer
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv("原文作者综合统计表.csv")

# 检查数据
print("数据形状:", df.shape)
print("列名:", df.columns.tolist())
print("前5行数据:")
print(df.head())
print("\n数据统计:")
print(df.describe())

# 使用实际的中文列名 (修改后的文件没有"总内容数"列)
features = [
    "原创总数", "转发总数", "被转发数总和",
    "被点赞数总和", "被评论数总和", "阅读数总和",
    "粉丝数总和"
]

print(f"\n使用的特征列: {features}")

# 检查是否有缺失值
print(f"\n缺失值检查:")
for feature in features:
    missing_count = df[feature].isnull().sum()
    print(f"{feature}: {missing_count} 个缺失值")

# 数据预处理和归一化
print(f"\n数据量: {len(df)} 个作者")

# 准备原始数据
X_raw = df[features].copy()

# 显示原始数据的分布特征
print(f"\n原始数据统计信息:")
print(X_raw.describe())

print(f"\n各特征的偏度和峰度:")
for feature in features:
    skewness = X_raw[feature].skew()
    kurtosis = X_raw[feature].kurtosis()
    print(f"{feature}: 偏度={skewness:.3f}, 峰度={kurtosis:.3f}")

# 检查异常值
print(f"\n异常值检查 (使用IQR方法):")
for feature in features:
    Q1 = X_raw[feature].quantile(0.25)
    Q3 = X_raw[feature].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    outliers = X_raw[(X_raw[feature] < lower_bound) | (X_raw[feature] > upper_bound)][feature]
    print(f"{feature}: {len(outliers)} 个异常值 ({len(outliers)/len(X_raw)*100:.1f}%)")

# 比较不同的归一化方法
print(f"\n=== 归一化方法比较 ===")

# 1. 标准化 (Z-score normalization)
print("1. 使用标准化 (StandardScaler)")
scaler_std = StandardScaler()
X_std = scaler_std.fit_transform(X_raw)
print(f"   标准化后均值: {np.mean(X_std, axis=0).round(3)}")
print(f"   标准化后标准差: {np.std(X_std, axis=0).round(3)}")

# 2. 最小-最大归一化
print("\n2. 使用最小-最大归一化 (MinMaxScaler)")
scaler_minmax = MinMaxScaler()
X_minmax = scaler_minmax.fit_transform(X_raw)
print(f"   归一化后最小值: {np.min(X_minmax, axis=0).round(3)}")
print(f"   归一化后最大值: {np.max(X_minmax, axis=0).round(3)}")

# 3. 鲁棒归一化
print("\n3. 使用鲁棒归一化 (RobustScaler)")
scaler_robust = RobustScaler()
X_robust = scaler_robust.fit_transform(X_raw)
print(f"   鲁棒归一化后中位数: {np.median(X_robust, axis=0).round(3)}")
print(f"   鲁棒归一化后IQR: {(np.percentile(X_robust, 75, axis=0) - np.percentile(X_robust, 25, axis=0)).round(3)}")

# 选择归一化方法
print(f"\n=== 选择归一化方法 ===")
print("基于数据特征分析，推荐使用:")

# 检查数据是否有很多异常值
total_outlier_ratio = sum([len(X_raw[(X_raw[feature] < X_raw[feature].quantile(0.25) - 1.5*(X_raw[feature].quantile(0.75)-X_raw[feature].quantile(0.25))) |
                                   (X_raw[feature] > X_raw[feature].quantile(0.75) + 1.5*(X_raw[feature].quantile(0.75)-X_raw[feature].quantile(0.25)))])
                          for feature in features]) / (len(X_raw) * len(features))

if total_outlier_ratio > 0.1:  # 如果异常值比例超过10%
    print("- 鲁棒归一化 (RobustScaler) - 因为数据中异常值较多")
    scaler = scaler_robust
    X_scaled = X_robust
    scaler_name = "RobustScaler"
else:
    print("- 标准化 (StandardScaler) - 因为数据分布相对正常")
    scaler = scaler_std
    X_scaled = X_std
    scaler_name = "StandardScaler"

print(f"选择的归一化方法: {scaler_name}")
print(f"异常值总比例: {total_outlier_ratio:.1%}")

# 使用PCA降维来提高计算效率
print(f"\n原始特征维度: {X_scaled.shape[1]}")
pca = PCA(n_components=0.95, random_state=42)  # 保留95%的方差
X_pca = pca.fit_transform(X_scaled)
print(f"PCA降维后维度: {X_pca.shape[1]}")
print(f"解释的方差比例: {pca.explained_variance_ratio_.sum():.3f}")

print(f"各主成分解释的方差比例:")
for i, ratio in enumerate(pca.explained_variance_ratio_):
    print(f"PC{i+1}: {ratio:.3f}")

# 使用降维后的数据进行聚类
X_for_clustering = X_pca

print(f"\n开始计算最优聚类数量...")
scores = []
K_range = range(2, 11)  # 扩展到11个聚类
for k in K_range:
    print(f"正在计算 K={k} 的轮廓系数...")
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10, max_iter=300)
    labels = kmeans.fit_predict(X_for_clustering)
    score = silhouette_score(X_for_clustering, labels)
    scores.append(score)
    print(f"K={k}, 轮廓系数={score:.3f}")

plt.figure(figsize=(10, 6))
plt.plot(K_range, scores, marker='o', linewidth=2, markersize=8)
plt.xlabel("聚类数量 (K)")
plt.ylabel("轮廓系数")
plt.title("轮廓系数 vs 聚类数量")
plt.grid(True, alpha=0.3)
plt.show()

optimal_k = K_range[np.argmax(scores)]
print(f"\n最优聚类数量: {optimal_k}")
print(f"对应的轮廓系数: {max(scores):.3f}")

# 使用最优K值进行聚类
kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
labels = kmeans.fit_predict(X_for_clustering)
df['聚类标签'] = labels

# 计算聚类中心（需要从PCA空间转换回原始特征空间）
# 先将PCA空间的聚类中心转换回标准化后的原始空间
pca_centers = kmeans.cluster_centers_
original_centers_scaled = pca.inverse_transform(pca_centers)
# 再转换回原始尺度
original_centers = scaler.inverse_transform(original_centers_scaled)

cluster_centers = pd.DataFrame(
    original_centers,
    columns=features
)
cluster_centers['聚类'] = range(optimal_k)

print("\n各聚类中心特征均值：")
print(cluster_centers)

# 计算每个聚类的样本数量
cluster_counts = df['聚类标签'].value_counts().sort_index()
print(f"\n各聚类样本数量:")
for i in range(optimal_k):
    print(f"聚类 {i}: {cluster_counts[i]} 个作者")

# 绘制聚类中心热力图
plt.figure(figsize=(14, 8))
heatmap_data = cluster_centers.set_index('聚类')
sns.heatmap(heatmap_data, cmap="YlOrRd", annot=True, fmt=".0f",
            cbar_kws={'label': '特征值'})
plt.title("各聚类特征均值热力图")
plt.ylabel("聚类")
plt.xlabel("特征")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 保存结果
output_filename = "用户行为聚类结果.csv"
df.to_csv(output_filename, index=False, encoding='utf-8-sig')
print(f"\n聚类结果已保存到: {output_filename}")

# 显示每个聚类的代表性作者
print(f"\n各聚类代表性作者:")
for i in range(optimal_k):
    cluster_data = df[df['聚类标签'] == i]
    print(f"\n聚类 {i} (共{len(cluster_data)}个作者):")
    # 显示该聚类中原创总数最多的前5个作者
    top_authors = cluster_data.nlargest(5, '原创总数')[['原文作者', '原创总数', '粉丝数总和', '阅读数总和']]
    print(top_authors.to_string(index=False))